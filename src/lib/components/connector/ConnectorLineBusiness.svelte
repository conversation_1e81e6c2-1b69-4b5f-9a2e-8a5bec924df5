<!-- ConnectionLineBusiness.svelte -->
<script lang="ts">
    import { Button, Modal, Input, Label, Radio, Toast, Dropzone } from "flowbite-svelte";
    import { CheckCircleSolid, ClipboardSolid, ChevronLeftOutline, ChevronRightOutline } from "flowbite-svelte-icons";
    import { fly } from 'svelte/transition';
    import { t } from '$lib/stores/i18n';
    import { enhance } from '$app/forms';

    export let showModal = false;
    export let connectionSettings;

    // Step management
    let currentStep = 1;
    const totalSteps = 2;

    // Step 1 - Connection Details (Required)
    let connectionName = "";
    let channelId = "";
    let channelSecret = "";
    let channelAccessToken = "";
    let lineProviderId = "";
    let lineProviderName = "";
    let isVerified = false;
    
    // Step 2 - QR Codes and Links (Optional)
    let lineOAQRCode = null;
    let lineOAQRLink = "";
    let lineGroupQRCode = null;
    let lineGroupQRLink = "";

    // Toast state
    let showToast = false;
    let toastMessage = "";
    let toastStatus = false;

    // Loading states for uploads
    let uploadingOALogo = false;
    let uploadingGroupLogo = false;

    /* ----------------------- Save or remove QR code via direct fetch ----------------------- */
    async function saveOASettings() {
        if (uploadingOALogo) return;
        uploadingOALogo = true;
        const removing = !lineOAQRCode;
        toastMessage = removing ? 'Removing LINE Official Account QR code...' : 'Saving LINE Official Account QR code...';
        showToast = true;
        timeout();

        try {
            const formData = new FormData();
            if (lineOAQRCode) {
                const fetchRes = await fetch(lineOAQRCode);
                const blob = await fetchRes.blob();
                formData.append('image_file', blob, 'line_oa_qr.png');
            } else {
                formData.append('image_file', new Blob([''], { type: 'text/plain' }), 'empty.txt');
            }
            formData.append('link', lineOAQRLink);
            formData.append('key', 'LINE_OA_QR_CODE');
            await fetch('?/upload_image', { method: 'POST', body: formData });
            toastMessage = removing ? 'LINE Official Account QR code removed!' : 'LINE Official Account QR code saved!';
        } catch (err) {
            toastMessage = `Error: ${err.message || 'Failed to update QR code'}`;
        } finally {
            uploadingOALogo = false;
            showToast = true;
            timeout();
        }
    }

    async function saveGroupSettings() {
        if (uploadingGroupLogo) return;
        uploadingGroupLogo = true;
        const removing = !lineGroupQRCode;
        toastMessage = removing ? 'Removing LINE Group QR code...' : 'Saving LINE Group QR code...';
        showToast = true;
        timeout();

        try {
            const formData = new FormData();
            if (lineGroupQRCode) {
                const fetchRes = await fetch(lineGroupQRCode);
                const blob = await fetchRes.blob();
                formData.append('image_file', blob, 'line_group_qr.png');
            } else {
                formData.append('image_file', new Blob([''], { type: 'text/plain' }), 'empty.txt');
            }
            formData.append('link', lineGroupQRLink);
            formData.append('key', 'LINE_GROUP_QR_CODE');
            await fetch('?/upload_image', { method: 'POST', body: formData });
            toastMessage = removing ? 'LINE Group QR code removed!' : 'LINE Group QR code saved!';
        } catch (err) {
            toastMessage = `Error: ${err.message || 'Failed to update QR code'}`;
        } finally {
            uploadingGroupLogo = false;
            showToast = true;
            timeout();
        }
    }
    
    // Form reference
    let formElement: HTMLFormElement;
    
    // Webhook URL (this would typically come from your backend)
    // let webhookUrl = "https://api.yourapp.com/webhook/line";

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
        }
    }

    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
        }
    }

    async function handleConnect() {
        // Validate only required fields (Step 1)
        if (!step1Valid) {
            toastMessage = t('please_fill_required_fields');
            showToast = true;
            setTimeout(() => {
                showToast = false;
            }, 3000);
            return;
        }
        
        try {
            // First save the main connection data (Step 1)
            const formData = new FormData();
            formData.append('name', connectionName);
            formData.append('channel_id', channelId);
            formData.append('channel_secret', channelSecret);
            formData.append('channel_access_token', channelAccessToken);
            formData.append('line_provider_id', lineProviderId);
            formData.append('line_provider_name', lineProviderName);
            
            const response = await fetch('?/create_line_connector', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                // If Step 2 has data, save QR codes separately
                if (lineOAQRCode || lineOAQRLink) {
                    await saveOASettings();
                }
                if (lineGroupQRCode || lineGroupQRLink) {
                    await saveGroupSettings();
                }
                
                toastMessage = t('connection_saved_successfully');
                showToast = true;
                setTimeout(() => {
                    showToast = false;
                }, 3000);
                showModal = false;
                resetForm();
            } else {
                toastMessage = t('connection_failed');
                showToast = true;
                setTimeout(() => {
                    showToast = false;
                }, 3000);
            }
        } catch (error) {
            toastMessage = t('connection_failed');
            showToast = true;
            setTimeout(() => {
                showToast = false;
            }, 3000);
        }
    }

    function handleCancel() {
        showModal = false;
        // Reset form
        connectionName = "";
        channelId = "";
        channelSecret = "";
        channelAccessToken = "";
        lineProviderId = "";
        lineProviderName = "";
        isVerified = false;
    }

    function resetForm() {
        currentStep = 1;
        connectionName = "";
        channelId = "";
        channelSecret = "";
        channelAccessToken = "";
        lineProviderId = "";
        lineProviderName = "";
        lineOAQRCode = null;
        lineOAQRLink = "";
        lineGroupQRCode = null;
        lineGroupQRLink = "";
    }
    
    // function copyWebhookUrl() {
    //     navigator.clipboard.writeText(webhookUrl);
    //     toastMessage = t('webhook_url_copied');
    //     showToast = true;
    //     setTimeout(() => {
    //         showToast = false;
    //     }, 2000);
    // }

    // File handling for QR codes
    function processFile(file: File, type: 'oa' | 'group') {
        const reader = new FileReader();
        reader.onload = (e) => {
            if (type === 'oa') {
                lineOAQRCode = e.target?.result as string;
            } else {
                lineGroupQRCode = e.target?.result as string;
            }
        };
        reader.readAsDataURL(file);
    }

    function handleDropzoneChange(e: Event, type: 'oa' | 'group') {
        const input = e.target as HTMLInputElement;
        if (input.files && input.files.length > 0) {
            processFile(input.files[0], type);
        }
    }

    function dropHandle(e: DragEvent, type: 'oa' | 'group') {
        e.preventDefault();
        if (e.dataTransfer?.items) {
            [...e.dataTransfer.items].forEach((item) => {
                if (item.kind === 'file') {
                    const file = item.getAsFile();
                    if (file) processFile(file, type);
                }
            });
        } else if (e.dataTransfer?.files) {
            [...e.dataTransfer.files].forEach((file) => {
                processFile(file, type);
            });
        }
    }

    function removeQRCode(type: 'oa' | 'group') {
        if (type === 'oa') {
            lineOAQRCode = null;
        } else {
            lineGroupQRCode = null;
        }
    }

    // Validation for each step
    $: step1Valid = connectionName.trim() !== "" && 
                   channelId.trim() !== "" && 
                   channelSecret.trim() !== "" && 
                   channelAccessToken.trim() !== "" && 
                   lineProviderId.trim() !== "" && 
                   lineProviderName.trim() !== "";

    // Step 2 is optional - always valid
    $: step2Valid = true;

    // Step status tracking
    $: step1Status = step1Valid ? 'completed' : (currentStep > 1 ? 'error' : 'current');
    $: step2Status = currentStep === 2 ? (step2Valid ? 'current' : 'current') : (currentStep > 2 ? (step2Valid ? 'completed' : 'error') : 'pending');

    function getStepClasses(step: number, status: string) {
        switch (status) {
            case 'completed':
                return 'bg-green-600 text-white border-2 border-green-600';
            case 'error':
                return 'bg-red-600 text-white border-2 border-red-600';
            case 'current':
                return 'bg-blue-600 text-white border-2 border-blue-600';
            case 'pending':
            default:
                return 'bg-gray-200 text-gray-600 border-2 border-gray-200';
        }
    }

    function getStepIcon(step: number, status: string) {
        switch (status) {
            case 'completed':
                return '✓';
            case 'error':
                return '✕';
            default:
                return step.toString();
        }
    }

    function getStepTextClasses(status: string) {
        switch (status) {
            case 'completed':
                return 'text-green-600 font-medium';
            case 'error':
                return 'text-red-600 font-medium';
            case 'current':
                return 'text-blue-600 font-medium';
            case 'pending':
            default:
                return 'text-gray-500';
        }
    }
</script>

<Modal bind:open={showModal} size="lg" autoclose={false} title={t('connect_line_business')} class="w-full">
    <!-- Step indicator -->
    <div slot="header" class="flex items-center justify-between w-full">
        <h3 class="text-xl font-semibold text-gray-900">
            {t('connect_line_business')}
        </h3>
    </div>

    <div class="flex items-center justify-center space-x-4 mb-6">
        <div class="flex items-center">
            <!-- Step 1 -->
            <div class="flex flex-col items-center">
                <button
                    type="button"
                    class="flex items-center justify-center w-10 h-10 rounded-full transition-colors cursor-pointer {getStepClasses(1, step1Status)}"
                    on:click={() => goToStep(1)}
                >
                    {getStepIcon(1, step1Status)}
                </button>
                <span class="text-sm {getStepTextClasses(step1Status)} mt-2">{t('connection_details')}</span>
            </div>
            
            <!-- Connecting Line -->
            <div class="w-32 h-px bg-gray-300 mx-4"></div>
            
            <!-- Step 2 -->
            <div class="flex flex-col items-center">
                <button
                    type="button"
                    class="flex items-center justify-center w-10 h-10 rounded-full transition-colors {step1Valid ? 'cursor-pointer' : 'cursor-not-allowed'} {getStepClasses(2, step2Status)}"
                    on:click={() => goToStep(2)}
                    disabled={!step1Valid}
                >
                    {getStepIcon(2, step2Status)}
                </button>
                <span class="text-sm {getStepTextClasses(step2Status)} mt-2">{t('qr_codes_and_links')}</span>
            </div>
        </div>
    </div>
    
    <form 
        bind:this={formElement}
        method="POST"
        action="?/create_line_connector"
        novalidate
        use:enhance={({ formData, submitter }) => {
            return async ({ result, update }) => {
                if (result.type === 'success') {
                    toastMessage = t('connection_saved_successfully');
                    showToast = true;
                    
                    // Auto hide toast after 3 seconds
                    setTimeout(() => {
                        showToast = false;
                    }, 3000);
                    
                    // Close modal and reset form
                    showModal = false;
                    handleCancel();
                } else if (result.type === 'failure') {
                    toastMessage = result.data?.error || t('connection_failed');
                    showToast = true;
                    setTimeout(() => {
                        showToast = false;
                    }, 3000);
                }
                await update();
            };
        }}
    >
        {#if currentStep === 1}
            <!-- Step 1: Connection Details -->
            <!-- Connection Name -->
            <div>
                <Label for="connection-name" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('connection_name')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="connection-name"
                    name="name"
                    bind:value={connectionName}
                    placeholder={t('connection_name_placeholder')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('connection_name_description')}</p> -->
            </div>

            <!-- Channel ID -->
            <div>
                <Label for="channel-id" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('channel_id')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="channel-id"
                    name="channel_id"
                    bind:value={channelId}
                    placeholder={t('channel_id_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('channel_id_description')}</p> -->
            </div>

            <!-- Channel Secret -->
            <div>
                <Label for="channel-secret" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('channel_secret')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="channel-secret"
                    name="channel_secret"
                    bind:value={channelSecret}
                    placeholder={t('channel_secret_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('channel_secret_description')}</p> -->
            </div>

            <!-- Channel Access Token -->
            <div>
                <Label for="channel-access-token" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('channel_access_token')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="channel-access-token"
                    name="channel_access_token"
                    bind:value={channelAccessToken}
                    placeholder={t('channel_access_token_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('channel_access_token_description')}</p> -->
            </div>

            <!-- LINE Provider ID -->
            <div>
                <Label for="line-provider-id" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('line_provider_id')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="line-provider-id"
                    name="line_provider_id"
                    bind:value={lineProviderId}
                    placeholder={t('line_provider_id_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('line_provider_id_description')}</p> -->
            </div>

            <!-- LINE Provider Name -->
            <div>
                <Label for="line-provider-name" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('line_provider_name')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="line-provider-name"
                    name="line_provider_name"
                    bind:value={lineProviderName}
                    placeholder={t('line_provider_name_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('line_provider_name_description')}</p> -->
            </div>

            <!-- Webhook Settings - Show only when Channel ID is filled -->
            <div>
                <div class="flex items-center gap-2">
                    <p class="text-sm text-gray-500">{t('webhook_settings_description')}</p>
                    <!-- {#if channelId}
                        <Button 
                            type="button"
                            color="alternative" 
                            size="sm" 
                            on:click={copyWebhookUrl}
                            class="px-2 py-1 text-sm"
                        >
                            <ClipboardSolid class="w-4 h-4 mr-1" />
                            {t('copy')}
                        </Button>
                    {/if} -->
                </div>
            </div>
        
        {:else if currentStep === 2}
            <!-- Step 2: QR Codes and Links (Optional) -->
            <div class="space-y-6">
                <p class="text-base leading-relaxed text-gray-500 mb-4">
                    {t('qr_codes_links_description')}
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- LINE Official Account -->
                    <div class="border rounded-lg p-4">
                        <h4 class="font-medium text-gray-700 mb-3">{t('line_official_account')}</h4>
                        <div class="space-y-4">
                            <!-- QR Code Upload -->
                            <div>
                                <Label class="block mb-2 text-sm font-medium text-gray-900">
                                    {t('qr_code')}
                                </Label>
                                <div class="w-full h-48">
                                    {#if lineOAQRCode}
                                        <div class="relative w-full h-full">
                                            <img
                                                src={lineOAQRCode}
                                                alt="LINE OA QR Code"
                                                class="w-full h-full object-contain border rounded-lg"
                                            />
                                            <button
                                                type="button"
                                                class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                                on:click={() => removeQRCode('oa')}
                                            >
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    {:else}
                                        <Dropzone
                                            id="dropzone-oa"
                                            class="h-full"
                                            on:drop={(e) => dropHandle(e, 'oa')}
                                            on:dragover={(e) => e.preventDefault()}
                                            on:change={(e) => handleDropzoneChange(e, 'oa')}
                                        >
                                            <div class="flex flex-col items-center justify-center h-full">
                                                <svg class="mb-2 w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                </svg>
                                                <p class="text-xs text-gray-500 text-center">{t('upload_qr')}</p>
                                                <p class="text-xs text-gray-500 text-center">SVG, PNG, JPG or GIF</p>
                                            </div>
                                        </Dropzone>
                                    {/if}
                                </div>
                            </div>
                            
                            <!-- OA Link -->
                            <div>
                                <Label for="oa-link" class="block mb-2 text-sm font-medium text-gray-900">
                                    {t('line_oa_link')}
                                </Label>
                                <Input
                                    id="oa-link"
                                    name="line_oa_link"
                                    bind:value={lineOAQRLink}
                                    placeholder={t('line_oa_link_placeholder')}
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- LINE Group -->
                    <div class="border rounded-lg p-4">
                        <h4 class="font-medium text-gray-700 mb-3">{t('line_group')}</h4>
                        <div class="space-y-4">
                            <!-- Group QR Code Upload -->
                            <div>
                                <Label class="block mb-2 text-sm font-medium text-gray-900">
                                    {t('qr_code')}
                                </Label>
                                <div class="w-full h-48">
                                    {#if lineGroupQRCode}
                                        <div class="relative w-full h-full">
                                            <img
                                                src={lineGroupQRCode}
                                                alt="LINE Group QR Code"
                                                class="w-full h-full object-contain border rounded-lg"
                                            />
                                            <button
                                                type="button"
                                                class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                                on:click={() => removeQRCode('group')}
                                            >
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    {:else}
                                        <Dropzone
                                            id="dropzone-group"
                                            class="h-full"
                                            on:drop={(e) => dropHandle(e, 'group')}
                                            on:dragover={(e) => e.preventDefault()}
                                            on:change={(e) => handleDropzoneChange(e, 'group')}
                                        >
                                            <div class="flex flex-col items-center justify-center h-full">
                                                <svg class="mb-2 w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                </svg>
                                                <p class="text-xs text-gray-500 text-center">{t('upload_qr')}</p>
                                                <p class="text-xs text-gray-500 text-center">SVG, PNG, JPG or GIF</p>
                                            </div>
                                        </Dropzone>
                                    {/if}
                                </div>
                            </div>
                            
                            <!-- Group Link -->
                            <div>
                                <Label for="group-link" class="block mb-2 text-sm font-medium text-gray-900">
                                    {t('line_group_link')}
                                </Label>
                                <Input
                                    id="group-link"
                                    name="line_group_link"
                                    bind:value={lineGroupQRLink}
                                    placeholder={t('line_group_link_placeholder')}
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {/if}

        <!-- Verified Radio Button -->
        <!-- <div>
            <Label class="block mb-2 text-sm font-medium text-gray-900">
                {t('verification_status')}
            </Label>
            <div class="flex items-center space-x-4">
                <Radio bind:group={isVerified} value={false} class="text-blue-600 focus:ring-blue-500">
                    {t('not_verified')}
                </Radio>
                <Radio bind:group={isVerified} value={true} class="text-blue-600 focus:ring-blue-500">
                    {t('verified')}
                </Radio>
            </div>
            <p class="mt-1 text-sm text-gray-500">{t('verification_status_description')}</p>
        </div> -->
        
        <!-- Hidden fields to submit all data -->
        <!-- QR codes will be handled separately via upload_image action -->
    </form>
    
    <svelte:fragment slot="footer">
        <div class="flex justify-between w-full">
            <div>
                <Button type="button" color="alternative" on:click={handleCancel} class="rounded-lg border border-gray-300 text-gray-700 bg-white hover:bg-gray-50">
                    {t('cancel')}
                </Button>
            </div>
            
            <div class="flex gap-2">
                {#if currentStep > 1}
                    <Button type="button" color="alternative" on:click={prevStep} class="rounded-lg border border-gray-300 text-gray-700 bg-white hover:bg-gray-50">
                        {t('previous')}
                    </Button>
                {/if}
                
                {#if currentStep < totalSteps}
                    <Button 
                        type="button" 
                        color="primary" 
                        on:click={nextStep}
                        disabled={currentStep === 1 && !step1Valid}
                        class="rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    >
                        {t('next')}
                    </Button>
                {:else}
                    <Button 
                        type="button" 
                        color="primary" 
                        on:click={handleConnect}
                        disabled={!step2Valid}
                        class="rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    >
                        {t('save')}
                    </Button>
                {/if}
            </div>
        </div>
    </svelte:fragment>
</Modal>

<!-- Success Toast -->
{#if showToast}
    <Toast
        color="green"
        transition={fly}
        params={{ x: 200 }}
        bind:toastStatus={showToast}
        class="fixed left-1/2 top-20 -translate-x-1/2 transform z-50"
    >
        <CheckCircleSolid slot="icon" class="h-5 w-5" />
        {toastMessage}
    </Toast>
{/if}